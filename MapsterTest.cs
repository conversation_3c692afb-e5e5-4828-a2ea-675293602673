using AcademicPerformance.Configurations;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Dtos;
using Mapster;

namespace AcademicPerformance
{
    /// <summary>
    /// Mapster mapping'lerini test etmek için basit test sınıfı
    /// </summary>
    public static class MapsterTest
    {
        public static void TestFeedbackMappings()
        {
            // MapsterConfig'i initialize et
            MapsterConfig.Configure();

            Console.WriteLine("=== Mapster Feedback Mapping Test ===");

            // Test 1: SubmissionFeedbackEntity -> FeedbackEntryDto
            var submissionFeedback = new SubmissionFeedbackEntity
            {
                Id = "test-id-1",
                SubmissionId = "submission-1",
                FeedbackType = "Approval",
                ControllerUserId = "controller-1",
                Message = "Test feedback message",
                SubmissionStatusAtTime = "Submitted",
                Status = "Completed",
                CreatedAt = DateTime.UtcNow,
                CreatedByUserId = "controller-1"
            };

            var feedbackDto = submissionFeedback.Adapt<FeedbackEntryDto>();

            Console.WriteLine($"✅ SubmissionFeedbackEntity -> FeedbackEntryDto");
            Console.WriteLine($"   ID: {feedbackDto.Id}");
            Console.WriteLine($"   Comments: {feedbackDto.Comments}");
            Console.WriteLine($"   FeedbackType: {feedbackDto.FeedbackType}");

            // Test 2: CriterionFeedbackEntity -> CriterionFeedbackDto
            var criterionFeedback = new CriterionFeedbackEntity
            {
                Id = "criterion-1",
                SubmissionFeedbackId = "feedback-1",
                SubmissionId = "submission-1",
                CriterionLinkId = "link-1",
                FeedbackMessage = "Criterion feedback test",
                Status = "Approved",
                Rating = 8
            };

            var criterionDto = criterionFeedback.Adapt<CriterionFeedbackDto>();

            Console.WriteLine($"✅ CriterionFeedbackEntity -> CriterionFeedbackDto");
            Console.WriteLine($"   ID: {criterionDto.Id}");
            Console.WriteLine($"   CriterionId: {criterionDto.CriterionId}");
            Console.WriteLine($"   FeedbackMessage: {criterionDto.FeedbackMessage}");
            Console.WriteLine($"   Rating: {criterionDto.Rating}");

            // Test 3: RevisionRequestDto -> SubmissionFeedbackEntity
            var revisionDto = new RevisionRequestDto
            {
                SubmissionId = "submission-2",
                GeneralMessage = "Please revise this submission",
                Priority = "High"
            };

            var revisionEntity = revisionDto.Adapt<SubmissionFeedbackEntity>();

            Console.WriteLine($"✅ RevisionRequestDto -> SubmissionFeedbackEntity");
            Console.WriteLine($"   SubmissionId: {revisionEntity.SubmissionId}");
            Console.WriteLine($"   Message: {revisionEntity.Message}");
            Console.WriteLine($"   FeedbackType: {revisionEntity.FeedbackType}");
            Console.WriteLine($"   Priority: {revisionEntity.Priority}");

            // Test 4: ApprovalFeedbackDto -> SubmissionFeedbackEntity
            var approvalDto = new ApprovalFeedbackDto
            {
                SubmissionId = "submission-3",
                Comments = "Excellent work!",
                OverallRating = 9,
                Highlights = "Great analysis"
            };

            var approvalEntity = approvalDto.Adapt<SubmissionFeedbackEntity>();

            Console.WriteLine($"✅ ApprovalFeedbackDto -> SubmissionFeedbackEntity");
            Console.WriteLine($"   SubmissionId: {approvalEntity.SubmissionId}");
            Console.WriteLine($"   Message: {approvalEntity.Message}");
            Console.WriteLine($"   FeedbackType: {approvalEntity.FeedbackType}");
            Console.WriteLine($"   OverallRating: {approvalEntity.OverallRating}");
            Console.WriteLine($"   Highlights: {approvalEntity.Highlights}");

            Console.WriteLine("\n🎉 Tüm Mapster mapping testleri başarılı!");
        }
    }
}
